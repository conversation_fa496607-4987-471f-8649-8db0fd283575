<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\InscriptionStudent;
use App\Models\Niveau;
use App\Models\Note;
use App\Models\Parcour;
use App\Models\User;
use Carbon\Carbon;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Validation\Rule;

class Etudiant extends Component
{
    use WithPagination;
    protected $paginationTheme = "bootstrap";
    
    // Remplacer le système de pages par un état modal
    public $showCreateModal = false;
    public $showEditModal = false;
    public $showDeleteModal = false;
    public $showRestoreModal = false;
    public $showAddInscriptionModal = false;
    public $viewSupprimesMode = false;
    
    public $selectedEtudiantId;
    public $selectedUserId;
    public $etuName;

    public $newUser = [];
    public $editUser = [];
    public $editParcours = [];

    // Propriétés pour l'ajout d'inscription
    public $newInscription = [];
    public $selectedUserForInscription = null;
    public $selectedUserName = '';

    public $query;
    public $filtreParcours;
    public $filtreNiveau;
    public $filtreAnnee;
    public $perPage = 10; // Ajouté pour permettre à l'utilisateur de choisir

    // Nouvelles propriétés pour améliorer l'UX
    public $isLoading = false;
    public $sortField = 'created_at';
    public $sortDirection = 'desc';
    public $showFilters = true;
    public $compactView = false;
    public $bulkAction = '';

    // Statistiques pour le dashboard
    public $totalEtudiants = 0;
    public $totalSupprimés = 0;

    protected $listeners = [
        "selectDate" => 'getSelectedDate',
        "refreshEtudiants" => '$refresh',
        "echo:students,StudentUpdated" => '$refresh'
    ];

    public function mount()
    {
        $this->loadStatistics();
    }

    // Règles de validation consolidées
    protected function rules()
    {
        $rules = [
            'nom' => 'required',
            'prenom' => 'required',
            'sexe' => 'required',
            'niveau_id' => 'required',
            'parcour_id' => 'required',
        ];
        
        // Conditionnels pour édition vs création
        if ($this->showEditModal) {
            $rules['telephone1'] = ['required', Rule::unique("users", "telephone1")->ignore($this->editUser['id'])];
        } else {
            $rules['telephone1'] = 'numeric|unique:users,telephone1';
        }
        
        return $rules;
    }

    // Réinitialisation de page lors de changement de filtres
    public function updatingQuery() {
        $this->resetPage();
        $this->isLoading = true;
    }
    public function updatingFiltreAnnee() {
        $this->resetPage();
        $this->loadStatistics();
    }
    public function updatingFiltreParcours() {
        $this->resetPage();
        $this->loadStatistics();
    }
    public function updatingFiltreNiveau() {
        $this->resetPage();
        $this->loadStatistics();
    }
    public function updatingPerPage() { $this->resetPage(); }

    // Nouvelles méthodes pour améliorer l'UX
    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
        $this->resetPage();
    }

    public function toggleFilters()
    {
        $this->showFilters = !$this->showFilters;
    }

    public function toggleCompactView()
    {
        $this->compactView = !$this->compactView;
    }

    public function clearAllFilters()
    {
        $this->query = '';
        $this->filtreParcours = '';
        $this->filtreNiveau = '';
        $this->filtreAnnee = '';
        $this->resetPage();
        $this->loadStatistics();
    }

    public function loadStatistics()
    {
        $baseQuery = InscriptionStudent::whereHas('user.roles', fn($q) => $q->where('role_id', 5));

        // Appliquer les mêmes filtres que la liste principale
        $baseQuery->when($this->filtreParcours, fn($q) => $q->where('parcour_id', $this->filtreParcours))
                  ->when($this->filtreNiveau, fn($q) => $q->where('niveau_id', $this->filtreNiveau))
                  ->when($this->filtreAnnee, fn($q) => $q->where('annee_universitaire_id', $this->filtreAnnee));

        $this->totalEtudiants = $baseQuery->count();
        $this->totalSupprimés = $baseQuery->onlyTrashed()->count();
    }

    public function render()
    {
        Carbon::setLocale("fr");

        // Optimisation: Utiliser des requêtes plus efficaces avec eager loading
        $persQuery = InscriptionStudent::query()
            ->with([
                'user:id,nom,prenom,telephone1,sexe,photo',
                'parcours:id,sigle,nom',
                'niveau:id,nom',
                'annee:id,nom'
            ])
            ->select(['id', 'user_id', 'parcour_id', 'niveau_id', 'annee_universitaire_id', 'created_at', 'deleted_at']);

        // Optimisation: Recherche plus efficace avec index
        if (!empty($this->query)) {
            $searchTerm = '%' . $this->query . '%';
            $persQuery->whereHas('user', function ($query) use ($searchTerm) {
                $query->where(function($q) use ($searchTerm) {
                    $q->where('nom', 'like', $searchTerm)
                      ->orWhere('prenom', 'like', $searchTerm)
                      ->orWhereRaw("CONCAT(nom, ' ', prenom) LIKE ?", [$searchTerm]);
                });
            });
        }

        // Appliquer les filtres de manière optimisée
        $persQuery->when($this->filtreParcours, fn($q) => $q->where('parcour_id', $this->filtreParcours))
                  ->when($this->filtreNiveau, fn($q) => $q->where('niveau_id', $this->filtreNiveau))
                  ->when($this->filtreAnnee, fn($q) => $q->where('annee_universitaire_id', $this->filtreAnnee));

        // Optimisation: Requête plus efficace pour les rôles
        $persQuery->whereHas('user.roles', fn($q) => $q->where('role_id', 5));

        // Appliquer le tri
        $persQuery->orderBy($this->sortField, $this->sortDirection);

        // Modifier la requête selon le mode actif
        if ($this->viewSupprimesMode) {
            $etudiants = $persQuery->onlyTrashed()
                ->paginate($this->perPage);
        } else {
            $etudiants = $persQuery->paginate($this->perPage);
        }

        // Charger les statistiques si nécessaire
        if (empty($this->totalEtudiants)) {
            $this->loadStatistics();
        }

        if ($this->showNotes) {
            return view('livewire.deraq.etudiant.notes', [
                'parcours' => $this->getCachedParcours(),
                'semestres' => \App\Models\Semestre::where('niveau_id', $this->current_niveau->id ?? null)
                    ->select(['id', 'nom'])
                    ->get(),
            ])->extends('layouts.backend')->section('content');
        }

        return view('livewire.deraq.etudiant.index', [
            "etudiants" => $etudiants,
            "parcours" => $this->getCachedParcours(),
            "annees" => $this->getCachedAnnees(),
            "niveaux" => $this->getCachedNiveaux(),
        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    // Méthodes de cache pour éviter les requêtes répétées
    protected function getCachedParcours()
    {
        return cache()->remember('parcours_list', 3600, function() {
            return Parcour::select(['id', 'sigle', 'nom'])->orderBy('sigle')->get();
        });
    }

    protected function getCachedAnnees()
    {
        return cache()->remember('annees_list', 3600, function() {
            return AnneeUniversitaire::select(['id', 'nom'])->orderBy('nom', 'desc')->get();
        });
    }

    protected function getCachedNiveaux()
    {
        return cache()->remember('niveaux_list', 3600, function() {
            return Niveau::select(['id', 'nom'])->orderBy('nom')->get();
        });
    }

    // Méthodes pour les modals
    public function openCreateModal()
    {
        $this->showCreateModal = true;
        $this->newUser = [];
        $this->dispatchBrowserEvent("helperDatePicker");
    }
    
    public function openEditModal($inscriptionId, $userId)
    {
        $this->editParcours = InscriptionStudent::find($inscriptionId)->toArray();
        $this->editUser = User::find($userId)->toArray();
        $this->showEditModal = true;
    }
    
    public function confirmDelete($name, $id)
    {
        $this->etuName = $name;
        $this->selectedEtudiantId = $id;
        $this->showDeleteModal = true;
        $this->dispatchBrowserEvent('openModal', ['modalId' => 'deleteConfirmModal']);
    }

    public function getSelectedDate($date)
    {
        $this->newUser["date_naissance"] = $date;
    }

    public function addUser()
    {
        // Validation avec nos règles
        $this->validate([
            'newUser.nom' => $this->rules()['nom'],
            'newUser.prenom' => $this->rules()['prenom'],
            'newUser.sexe' => $this->rules()['sexe'],
            'newUser.telephone1' => $this->rules()['telephone1'],
            'newUser.niveau_id' => $this->rules()['niveau_id'],
            'newUser.parcour_id' => $this->rules()['parcour_id'],
        ]);

        $this->newUser["photo"] = "media/avatars/avatar0.jpg";

        // Ajouter un nouvel utilisateur
        $user = User::create($this->newUser);
        $user->roles()->attach(5);

        InscriptionStudent::create([
            "user_id" => $user->id,
            "annee_universitaire_id" => 4, // À remplacer par l'année courante idéalement
            "niveau_id" => $user->niveau_id,
            "parcour_id" => $user->parcour_id,
        ]);

        $this->newUser = [];
        $this->showCreateModal = false;
        $this->dispatchBrowserEvent('closeModal', ['modalId' => 'createEtudiantModal']);
        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Etudiant créé avec succès!"]);
    }

    public function updateUser()
    {
        // Validation avec nos règles
        $this->validate([
            'editUser.nom' => $this->rules()['nom'],
            'editUser.prenom' => $this->rules()['prenom'],
            'editUser.sexe' => $this->rules()['sexe'],
            'editUser.telephone1' => $this->rules()['telephone1'],
            'editParcours.niveau_id' => $this->rules()['niveau_id'],
            'editParcours.parcour_id' => $this->rules()['parcour_id'],
        ]);

        $insc = InscriptionStudent::find($this->editParcours["id"]);
        $insc->user()->update([
            "nom" => $this->editUser["nom"],
            "prenom" => $this->editUser["prenom"],
            "telephone1" => $this->editUser["telephone1"],
            "sexe" => $this->editUser["sexe"],
        ]);
        
        $insc->update([
            "niveau_id" => $this->editParcours["niveau_id"],
            "parcour_id" => $this->editParcours["parcour_id"],
        ]);

        $this->showEditModal = false;
        $this->dispatchBrowserEvent('closeModal', ['modalId' => 'editEtudiantModal']);
        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Utilisateur mis à jour avec succès!"]);
    }

    public function deleteUser()
    {
        InscriptionStudent::destroy($this->selectedEtudiantId);
        $this->showDeleteModal = false;
        $this->dispatchBrowserEvent('closeModal', ['modalId' => 'deleteConfirmModal']);
        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Utilisateur supprimé avec succès!"]);
        $this->loadStatistics();
    }

    public function toggleSupprimesMode()
    {
        $this->viewSupprimesMode = !$this->viewSupprimesMode;
        $this->resetPage();
    }

    // Méthodes pour l'ajout d'inscription
    public function openAddInscriptionModal($userId, $userName)
    {
        $this->selectedUserForInscription = $userId;
        $this->selectedUserName = $userName;
        $this->newInscription = [];
        $this->showAddInscriptionModal = true;
    }

    public function closeAddInscriptionModal()
    {
        $this->showAddInscriptionModal = false;
        $this->selectedUserForInscription = null;
        $this->selectedUserName = '';
        $this->newInscription = [];
    }

    public function addInscription()
    {
        $this->validate([
            'newInscription.niveau_id' => 'required|exists:niveaux,id',
            'newInscription.parcour_id' => 'required|exists:parcours,id',
            'newInscription.annee_universitaire_id' => 'required|exists:annee_universitaires,id',
        ]);

        try {
            $user = User::findOrFail($this->selectedUserForInscription);

            // Vérifier si l'inscription existe déjà pour cette année
            $existingInscription = InscriptionStudent::where('user_id', $user->id)
                ->where('annee_universitaire_id', $this->newInscription['annee_universitaire_id'])
                ->first();

            if ($existingInscription) {
                $this->dispatchBrowserEvent('showErrorMessage', [
                    'message' => 'Cet étudiant est déjà inscrit pour cette année universitaire.'
                ]);
                return;
            }

            // Créer la nouvelle inscription
            InscriptionStudent::create([
                'user_id' => $user->id,
                'niveau_id' => $this->newInscription['niveau_id'],
                'parcour_id' => $this->newInscription['parcour_id'],
                'annee_universitaire_id' => $this->newInscription['annee_universitaire_id'],
            ]);

            $this->closeAddInscriptionModal();
            $this->loadStatistics();
            $this->dispatchBrowserEvent('showSuccessMessage', [
                'message' => 'Inscription ajoutée avec succès pour ' . $this->selectedUserName
            ]);

        } catch (\Exception $e) {
            $this->dispatchBrowserEvent('showErrorMessage', [
                'message' => 'Erreur lors de l\'ajout de l\'inscription: ' . $e->getMessage()
            ]);
        }
    }


    
    // Actions rapides
    public function viewNotes($userId)
    {
        return redirect()->route('deraq.gestions.etudiants.notes', ['userId' => $userId]);
    }
    
    public function viewReleve($userId, $parcourId, $niveauId, $anneeId)
    {
        return redirect()->route('deraq.gestions.etudiants.releve', [
            'userId' => $userId,
            'parcourId' => $parcourId,
            'niveauId' => $niveauId,
            'anneeId' => $anneeId
        ]);
    }
    
    // Action groupée pour la suppression et restauration
    public $selectedItems = [];

    public function deleteSelected()
    {
        if(count($this->selectedItems) > 0) {
            InscriptionStudent::whereIn('id', $this->selectedItems)->delete();
            $this->dispatchBrowserEvent("showSuccessMessage", ["message" => count($this->selectedItems) . " étudiant(s) supprimé(s) avec succès!"]);
            $this->selectedItems = [];
            $this->loadStatistics();
        }
    }

    public function restoreSelected()
    {
        if(count($this->selectedItems) > 0) {
            InscriptionStudent::onlyTrashed()->whereIn('id', $this->selectedItems)->restore();
            $this->dispatchBrowserEvent("showSuccessMessage", ["message" => count($this->selectedItems) . " étudiant(s) restauré(s) avec succès!"]);
            $this->selectedItems = [];
            $this->loadStatistics();
        }
    }

    // Nouvelles fonctionnalités avancées
    public function exportStudents()
    {
        $this->isLoading = true;

        try {
            // Construire la requête avec les mêmes filtres que l'affichage
            $query = InscriptionStudent::query()
                ->with(['user:id,nom,prenom,telephone1,sexe,email', 'parcours:id,sigle,nom', 'niveau:id,nom', 'annee:id,nom']);

            // Appliquer les filtres
            if (!empty($this->query)) {
                $searchTerm = '%' . $this->query . '%';
                $query->whereHas('user', function ($q) use ($searchTerm) {
                    $q->where(function($subQ) use ($searchTerm) {
                        $subQ->where('nom', 'like', $searchTerm)
                             ->orWhere('prenom', 'like', $searchTerm)
                             ->orWhereRaw("CONCAT(nom, ' ', prenom) LIKE ?", [$searchTerm]);
                    });
                });
            }

            $query->when($this->filtreParcours, fn($q) => $q->where('parcour_id', $this->filtreParcours))
                  ->when($this->filtreNiveau, fn($q) => $q->where('niveau_id', $this->filtreNiveau))
                  ->when($this->filtreAnnee, fn($q) => $q->where('annee_universitaire_id', $this->filtreAnnee))
                  ->whereHas('user.roles', fn($q) => $q->where('role_id', 5));

            if ($this->viewSupprimesMode) {
                $etudiants = $query->onlyTrashed()->get();
            } else {
                $etudiants = $query->get();
            }

            // Créer le fichier CSV
            $filename = 'etudiants_' . date('Y-m-d_H-i-s') . '.csv';
            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            ];

            $callback = function() use ($etudiants) {
                $file = fopen('php://output', 'w');

                // En-têtes CSV
                fputcsv($file, [
                    'Nom',
                    'Prénom',
                    'Sexe',
                    'Téléphone',
                    'Email',
                    'Parcours',
                    'Niveau',
                    'Année universitaire',
                    'Date inscription'
                ]);

                // Données
                foreach ($etudiants as $etu) {
                    fputcsv($file, [
                        $etu->user->nom,
                        $etu->user->prenom,
                        $etu->user->sexe === 'M' ? 'Homme' : 'Femme',
                        $etu->user->telephone1,
                        $etu->user->email,
                        $etu->parcours?->sigle . ' - ' . $etu->parcours?->nom,
                        $etu->niveau->nom,
                        $etu->annee->nom,
                        $etu->created_at->format('d/m/Y H:i')
                    ]);
                }

                fclose($file);
            };

            $this->dispatchBrowserEvent('downloadFile', [
                'url' => route('students.export', [
                    'query' => $this->query,
                    'parcours' => $this->filtreParcours,
                    'niveau' => $this->filtreNiveau,
                    'annee' => $this->filtreAnnee,
                    'deleted' => $this->viewSupprimesMode
                ])
            ]);

            $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Export en cours de téléchargement..."]);

        } catch (\Exception $e) {
            $this->dispatchBrowserEvent("showErrorMessage", ["message" => "Erreur lors de l'export: " . $e->getMessage()]);
        } finally {
            $this->isLoading = false;
        }
    }

    public function bulkUpdateParcours($parcourId)
    {
        if(count($this->selectedItems) > 0 && $parcourId) {
            InscriptionStudent::whereIn('id', $this->selectedItems)
                ->update(['parcour_id' => $parcourId]);

            $this->dispatchBrowserEvent("showSuccessMessage", [
                "message" => count($this->selectedItems) . " étudiant(s) mis à jour avec succès!"
            ]);
            $this->selectedItems = [];
        }
    }

    public function bulkUpdateNiveau($niveauId)
    {
        if(count($this->selectedItems) > 0 && $niveauId) {
            InscriptionStudent::whereIn('id', $this->selectedItems)
                ->update(['niveau_id' => $niveauId]);

            $this->dispatchBrowserEvent("showSuccessMessage", [
                "message" => count($this->selectedItems) . " étudiant(s) mis à jour avec succès!"
            ]);
            $this->selectedItems = [];
        }
    }

    public function exportSelected()
    {
        if(count($this->selectedItems) > 0) {
            $this->isLoading = true;

            try {
                $etudiants = InscriptionStudent::whereIn('id', $this->selectedItems)
                    ->with(['user:id,nom,prenom,telephone1,sexe,email', 'parcours:id,sigle,nom', 'niveau:id,nom', 'annee:id,nom'])
                    ->get();

                $this->dispatchBrowserEvent('downloadFile', [
                    'url' => route('students.export.selected', ['ids' => implode(',', $this->selectedItems)])
                ]);

                $this->dispatchBrowserEvent("showSuccessMessage", [
                    "message" => "Export de " . count($this->selectedItems) . " étudiant(s) en cours..."
                ]);

            } catch (\Exception $e) {
                $this->dispatchBrowserEvent("showErrorMessage", [
                    "message" => "Erreur lors de l'export: " . $e->getMessage()
                ]);
            } finally {
                $this->isLoading = false;
            }
        }
    }

    public function forceDeleteSelected()
    {
        if(count($this->selectedItems) > 0) {
            InscriptionStudent::onlyTrashed()
                ->whereIn('id', $this->selectedItems)
                ->forceDelete();

            $this->dispatchBrowserEvent("showSuccessMessage", [
                "message" => count($this->selectedItems) . " étudiant(s) supprimé(s) définitivement!"
            ]);
            $this->selectedItems = [];
            $this->loadStatistics();
        }
    }

    // Amélioration des méthodes existantes avec feedback
    public function restoreEtu($id)
    {
        $etu = InscriptionStudent::onlyTrashed()->findOrFail($id);
        $etu->restore();
        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Étudiant restauré avec succès!"]);
        $this->loadStatistics();
    }

    public function deleteDefEtu($id)
    {
        $etu = InscriptionStudent::onlyTrashed()->findOrFail($id);
        $etu->forceDelete();
        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Étudiant supprimé définitivement avec succès!"]);
        $this->loadStatistics();
    }
    
    // --- Gestion des notes (page dédiée) ---
    public $showNotes = false;
    public $selectedStudentName = '';
    public $noteUserId = null;
    public $noteParcourId = null;
    public $noteMatiereId = null;
    public $noteTypeId = null;
    public $noteValeur = null;
    public $noteObservation = null;
    public $noteActionMode = 'list'; // list|create|edit
    public $selectedNoteId = null;
    public $notes = [];
    public $matieres = [];
    public $noteTypes = [];
    public $current_semestre;
    public $current_niveau;
    public $current_annee;
    public $current_parcour;
    public $selectedSemestreId = '';

    // Nouvelles propriétés pour l'amélioration UX des notes
    public $viewMode = 'table'; // table|cards|chart
    public $searchQuery = '';
    public $sortBy = 'created_at_desc';
    public $selectedNoteType = '';
    public $autoSave = true;
    public $saveStatus = [];
    public $notesStatistics = [];

    // Validation en temps réel
    public $validationErrors = [];
    public $isValidating = false;

    public function showNotes($userId, $parcours, $niveau, $annee, $name)
    {
        $this->noteUserId = $userId;
        $this->selectedStudentName = $name;
        $this->showNotes = true;
        $this->noteActionMode = 'list';
        $this->noteTypes = \App\Models\TypeNote::all();
        $this->resetNoteForm();
        $inscription = \App\Models\InscriptionStudent::where('user_id', $userId)
            ->with(['parcours', 'niveau', 'annee'])
            ->first();
        if ($inscription) {
            $this->current_parcour = \App\Models\Parcour::findOrFail($parcours);
            $this->current_niveau = \App\Models\Niveau::findOrFail($niveau);
            $this->current_annee = \App\Models\AnneeUniversitaire::findOrFail($annee);
            $this->current_semestre = \App\Models\Semestre::where('niveau_id', $inscription->niveau_id)->first();
        }
        $this->loadNotes();
    }

    public function backToList()
    {
        $this->showNotes = false;
        $this->resetNoteForm();
    }

    public function loadNotes()
    {
        if ($this->noteUserId && $this->current_parcour && $this->current_niveau && $this->current_annee) {
            $query = \App\Models\Note::with(['matiere', 'typeNote'])
                ->where('user_id', $this->noteUserId)
                ->whereHas('matiere.ue', function($q) {
                    $q->where('parcour_id', $this->current_parcour->id)
                      ->where('niveau_id', $this->current_niveau->id)
                      ->where('annee_universitaire_id', $this->current_annee->id);
                });

            // Filtres améliorés
            if ($this->selectedSemestreId) {
                $query = $query->whereHas('matiere.ue', function($q) {
                    $q->where('semestre_id', $this->selectedSemestreId);
                });
            }

            if ($this->selectedNoteType) {
                $query = $query->where('type_note_id', $this->selectedNoteType);
            }

            if ($this->searchQuery) {
                $query = $query->where(function($q) {
                    $q->whereHas('matiere', function($subQ) {
                        $subQ->where('nom', 'like', '%' . $this->searchQuery . '%');
                    })
                    ->orWhereHas('typeNote', function($subQ) {
                        $subQ->where('nom', 'like', '%' . $this->searchQuery . '%');
                    })
                    ->orWhere('observation', 'like', '%' . $this->searchQuery . '%');
                });
            }

            // Tri amélioré
            switch($this->sortBy) {
                case 'created_at_asc':
                    $query = $query->orderBy('created_at', 'asc');
                    break;
                case 'valeur_desc':
                    $query = $query->orderBy('valeur', 'desc');
                    break;
                case 'valeur_asc':
                    $query = $query->orderBy('valeur', 'asc');
                    break;
                case 'matiere_asc':
                    $query = $query->join('matieres', 'notes.matiere_id', '=', 'matieres.id')
                           ->orderBy('matieres.nom', 'asc')
                           ->select('notes.*');
                    break;
                default:
                    $query = $query->orderBy('created_at', 'desc');
            }

            $this->notes = $query->get();
            $this->calculateNotesStatistics();
        } else {
            $this->notes = collect();
        }
    }

    // Nouvelles méthodes pour les statistiques
    public function calculateNotesStatistics()
    {
        if ($this->notes->count() > 0) {
            $this->notesStatistics = [
                'average' => $this->notes->avg('valeur'),
                'best' => $this->notes->max('valeur'),
                'worst' => $this->notes->min('valeur'),
                'count' => $this->notes->count(),
                'success_rate' => ($this->notes->where('valeur', '>=', 10)->count() / $this->notes->count()) * 100
            ];
        } else {
            $this->notesStatistics = [
                'average' => 0,
                'best' => 0,
                'worst' => 0,
                'count' => 0,
                'success_rate' => 0
            ];
        }
    }

    public function calculateAverage()
    {
        return $this->notesStatistics['average'] ?? 0;
    }

    public function getBestNote()
    {
        return $this->notesStatistics['best'] ?? 0;
    }

    public function getSuccessRate()
    {
        return round($this->notesStatistics['success_rate'] ?? 0);
    }

    public function updateParcour($value, $matiereId = null)
    {
        $semestreId = $this->selectedSemestreId ?: ($this->current_semestre->id ?? null);
        $this->matieres = \App\Models\Matiere::whereHas('ue', function($q) use ($semestreId, $value) {
            if ($semestreId) {
                $q->whereSemestreId($semestreId);
            }
            $q->whereParcourId($value)
              ->whereNiveauId($this->current_niveau->id)
              ->whereAnneeUniversitaireId($this->current_annee->id);
        })->get(['id', 'nom']);
        // Si une matière est passée et existe dans la nouvelle liste, la sélectionner, sinon prendre la première
        if ($matiereId && $this->matieres->contains('id', $matiereId)) {
            $this->noteMatiereId = $matiereId;
        } else {
            $this->noteMatiereId = $this->matieres->first()->id ?? null;
        }
    }

    public function resetNoteForm()
    {
        $this->noteParcourId = null;
        $this->noteMatiereId = null;
        $this->noteTypeId = null;
        $this->noteValeur = null;
        $this->noteObservation = null;
        $this->selectedNoteId = null;
        $this->matieres = [];
    }

    public function showCreateNoteForm()
    {
        $this->resetNoteForm();
        $this->noteActionMode = 'create';
        // Pré-sélectionne le parcours courant si dispo
        if ($this->current_parcour) {
            $this->noteParcourId = $this->current_parcour->id;
            $this->updateParcour($this->noteParcourId);
        } else {
            $this->noteParcourId = null;
            $this->matieres = [];
        }
    }

    public function createNote()
    {
        $this->isValidating = true;

        try {
            $this->validate([
                'noteMatiereId' => 'required|exists:matieres,id',
                'noteTypeId' => 'required|exists:type_notes,id',
                'noteValeur' => 'required|numeric|min:0|max:20',
            ]);

            \App\Models\Note::create([
                'user_id' => $this->noteUserId,
                'matiere_id' => $this->noteMatiereId,
                'type_note_id' => $this->noteTypeId,
                'valeur' => $this->noteValeur,
                'observation' => $this->noteObservation,
            ]);

            $this->loadNotes();
            $this->noteActionMode = 'list';
            $this->resetNoteForm();
            $this->dispatchBrowserEvent('showSuccessMessage', ['message' => 'Note ajoutée avec succès']);

        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->validationErrors = $e->errors();
            $this->dispatchBrowserEvent('showErrorMessage', ['message' => 'Veuillez corriger les erreurs de saisie']);
        } finally {
            $this->isValidating = false;
        }
    }

    // Validation en temps réel
    public function updatedNoteValeur()
    {
        $this->validateOnly('noteValeur', [
            'noteValeur' => 'required|numeric|min:0|max:20',
        ]);

        if ($this->autoSave && $this->selectedNoteId) {
            $this->autoSaveNote();
        }
    }

    public function updatedNoteObservation()
    {
        if ($this->autoSave && $this->selectedNoteId) {
            $this->autoSaveNote();
        }
    }

    // Auto-sauvegarde
    public function autoSaveNote()
    {
        if (!$this->selectedNoteId) return;

        $this->saveStatus[$this->selectedNoteId] = 'saving';

        try {
            $note = \App\Models\Note::findOrFail($this->selectedNoteId);
            $note->update([
                'valeur' => $this->noteValeur,
                'observation' => $this->noteObservation,
            ]);

            $this->saveStatus[$this->selectedNoteId] = 'saved';
            $this->loadNotes();

            // Effacer le statut après 2 secondes
            $this->dispatchBrowserEvent('clearSaveStatus', ['noteId' => $this->selectedNoteId]);

        } catch (\Exception $e) {
            $this->saveStatus[$this->selectedNoteId] = 'error';
            $this->dispatchBrowserEvent('showErrorMessage', ['message' => 'Erreur lors de la sauvegarde']);
        }
    }

    public function showEditNoteForm($noteId)
    {
        $note = \App\Models\Note::with(['matiere', 'typeNote'])->findOrFail($noteId);
        $this->selectedNoteId = $note->id;
        $this->noteMatiereId = $note->matiere_id;
        $this->noteTypeId = $note->type_note_id;
        $this->noteValeur = $note->valeur;
        $this->noteObservation = $note->observation;

        // Récupère le parcours de la matière
        $matiere = \App\Models\Matiere::with('ue.parcours')->find($note->matiere_id);
        if ($matiere && $matiere->ue && $matiere->ue->parcours) {
            $this->noteParcourId = $matiere->ue->parcours->id;
            $this->updateParcour($this->noteParcourId, $note->matiere_id);
        } else {
            $this->noteParcourId = null;
            $this->matieres = [];
        }

        $this->noteActionMode = 'edit';
    }

    public function updateNote()
    {
        $this->isValidating = true;

        try {
            $this->validate([
                'noteMatiereId' => 'required|exists:matieres,id',
                'noteTypeId' => 'required|exists:type_notes,id',
                'noteValeur' => 'required|numeric|min:0|max:20',
            ]);

            $note = \App\Models\Note::findOrFail($this->selectedNoteId);
            $note->update([
                'matiere_id' => $this->noteMatiereId,
                'type_note_id' => $this->noteTypeId,
                'valeur' => $this->noteValeur,
                'observation' => $this->noteObservation,
            ]);

            $this->loadNotes();
            $this->noteActionMode = 'list';
            $this->resetNoteForm();
            $this->dispatchBrowserEvent('showSuccessMessage', ['message' => 'Note modifiée avec succès']);

        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->validationErrors = $e->errors();
            $this->dispatchBrowserEvent('showErrorMessage', ['message' => 'Veuillez corriger les erreurs de saisie']);
        } finally {
            $this->isValidating = false;
        }
    }

    // Méthode unifiée pour sauvegarder (création ou modification)
    public function saveNote()
    {
        if ($this->noteActionMode === 'create') {
            $this->createNote();
        } elseif ($this->noteActionMode === 'edit') {
            $this->updateNote();
        }
    }

    public function deleteNote($noteId)
    {
        $note = \App\Models\Note::findOrFail($noteId);
        $note->delete();
        $this->loadNotes();
        $this->dispatchBrowserEvent('showSuccessMessage', ['message' => 'Note supprimée avec succès']);
    }

    public function filterBySemestre()
    {
        $this->loadNotes();
    }

    // Nouvelles méthodes pour les filtres et actions
    public function applySorting()
    {
        $this->loadNotes();
    }

    public function updatedSearchQuery()
    {
        $this->loadNotes();
    }

    public function updatedSelectedNoteType()
    {
        $this->loadNotes();
    }

    public function exportNotes()
    {
        try {
            $filename = 'notes_' . str_replace(' ', '_', $this->selectedStudentName) . '_' . date('Y-m-d_H-i-s') . '.csv';

            $this->dispatchBrowserEvent('downloadFile', [
                'url' => route('notes.export', [
                    'userId' => $this->noteUserId,
                    'parcourId' => $this->current_parcour->id,
                    'niveauId' => $this->current_niveau->id,
                    'anneeId' => $this->current_annee->id,
                    'semestreId' => $this->selectedSemestreId,
                    'noteType' => $this->selectedNoteType,
                    'search' => $this->searchQuery
                ])
            ]);

            $this->dispatchBrowserEvent('showSuccessMessage', ['message' => 'Export des notes en cours...']);

        } catch (\Exception $e) {
            $this->dispatchBrowserEvent('showErrorMessage', ['message' => 'Erreur lors de l\'export']);
        }
    }

    // Méthodes pour les raccourcis clavier
    public function quickAddNote()
    {
        $this->showCreateNoteForm();
    }

    public function quickSave()
    {
        if ($this->noteActionMode === 'create') {
            $this->createNote();
        } elseif ($this->noteActionMode === 'edit') {
            $this->updateNote();
        }
    }

    public function quickCancel()
    {
        $this->noteActionMode = 'list';
        $this->resetNoteForm();
    }

    // Amélioration de la méthode de suppression avec confirmation
    public function confirmDeleteNote($noteId)
    {
        $this->selectedNoteId = $noteId;
        $this->dispatchBrowserEvent('confirmDelete', [
            'title' => 'Supprimer cette note ?',
            'message' => 'Cette action est irréversible.',
            'confirmText' => 'Supprimer',
            'cancelText' => 'Annuler'
        ]);
    }

    public function deleteNoteConfirmed()
    {
        if ($this->selectedNoteId) {
            $note = \App\Models\Note::findOrFail($this->selectedNoteId);
            $note->delete();
            $this->loadNotes();
            $this->selectedNoteId = null;
            $this->dispatchBrowserEvent('showSuccessMessage', ['message' => 'Note supprimée avec succès']);
        }
    }
}