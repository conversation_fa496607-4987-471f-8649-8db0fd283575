<!-- Modal d'ajout d'inscription - Version Livewire Pure -->
<?php if($showAddInscriptionModal): ?>
<div class="livewire-modal-overlay" wire:click="closeAddInscriptionModal">
    <div class="livewire-modal-content modal-lg" wire:click.stop>
        <div class="modal-header bg-success text-white">
            <h5 class="modal-title">
                <i class="fa fa-user-plus me-2"></i>
                Ajouter une inscription
            </h5>
            <button type="button" class="btn-close btn-close-white" wire:click="closeAddInscriptionModal" aria-label="Close"></button>
        </div>
        
        <div class="modal-body">
            <!-- Informations de l'étudiant -->
            <div class="alert alert-info mb-4">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <div class="student-avatar">
                            <i class="fa fa-user fa-2x"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="alert-heading mb-1">
                            <i class="fa fa-info-circle me-1"></i>
                            Étudiant sélectionné
                        </h6>
                        <div class="fw-bold"><?php echo e($selectedUserName); ?></div>
                        <small class="text-muted">
                            <i class="fa fa-lightbulb me-1"></i>
                            Ajoutez une nouvelle inscription pour cet étudiant
                        </small>
                    </div>
                </div>
            </div>

            <form wire:submit.prevent="addInscription">
                <div class="row g-3">
                    <!-- Année universitaire -->
                    <div class="col-md-6">
                        <label class="form-label fw-semibold">
                            <i class="fa fa-calendar me-1 text-success"></i>
                            Année universitaire <span class="text-danger">*</span>
                        </label>
                        <select class="form-select <?php $__errorArgs = ['newInscription.annee_universitaire_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                wire:model="newInscription.annee_universitaire_id">
                            <option value="">-- Sélectionner une année --</option>
                            <?php $__currentLoopData = $annees ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $annee): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($annee->id); ?>"><?php echo e($annee->nom); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php $__errorArgs = ['newInscription.annee_universitaire_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Parcours -->
                    <div class="col-md-6">
                        <label class="form-label fw-semibold">
                            <i class="fa fa-graduation-cap me-1 text-success"></i>
                            Parcours <span class="text-danger">*</span>
                        </label>
                        <select class="form-select <?php $__errorArgs = ['newInscription.parcour_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                wire:model="newInscription.parcour_id">
                            <option value="">-- Sélectionner un parcours --</option>
                            <?php $__currentLoopData = $parcours ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($parc->id); ?>"><?php echo e($parc->sigle); ?> - <?php echo e($parc->nom); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php $__errorArgs = ['newInscription.parcour_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Niveau -->
                    <div class="col-md-6">
                        <label class="form-label fw-semibold">
                            <i class="fa fa-layer-group me-1 text-success"></i>
                            Niveau <span class="text-danger">*</span>
                        </label>
                        <select class="form-select <?php $__errorArgs = ['newInscription.niveau_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                wire:model="newInscription.niveau_id">
                            <option value="">-- Sélectionner un niveau --</option>
                            <?php $__currentLoopData = $niveaux ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $niveau): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($niveau->id); ?>"><?php echo e($niveau->nom); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php $__errorArgs = ['newInscription.niveau_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Informations supplémentaires -->
                    <div class="col-md-6">
                        <div class="card border-0 bg-light h-100">
                            <div class="card-body">
                                <h6 class="card-title text-success">
                                    <i class="fa fa-info-circle me-1"></i>
                                    Informations
                                </h6>
                                <ul class="list-unstyled mb-0 small">
                                    <li><i class="fa fa-check text-success me-1"></i> L'inscription sera créée immédiatement</li>
                                    <li><i class="fa fa-exclamation-triangle text-warning me-1"></i> Vérifiez que l'étudiant n'est pas déjà inscrit</li>
                                    <li><i class="fa fa-user-graduate text-info me-1"></i> L'étudiant pourra accéder aux cours</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Aperçu de l'inscription -->
                    <?php if($newInscription['annee_universitaire_id'] ?? false && $newInscription['parcour_id'] ?? false && $newInscription['niveau_id'] ?? false): ?>
                    <div class="col-12">
                        <div class="alert alert-success">
                            <h6 class="alert-heading">
                                <i class="fa fa-eye me-1"></i>Aperçu de l'inscription
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Étudiant:</strong> <?php echo e($selectedUserName); ?><br>
                                    <strong>Année:</strong> 
                                    <?php $__currentLoopData = $annees ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $annee): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php if($annee->id == $newInscription['annee_universitaire_id']): ?>
                                            <?php echo e($annee->nom); ?>

                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                                <div class="col-md-6">
                                    <strong>Parcours:</strong> 
                                    <?php $__currentLoopData = $parcours ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php if($parc->id == $newInscription['parcour_id']): ?>
                                            <?php echo e($parc->sigle); ?> - <?php echo e($parc->nom); ?>

                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <br>
                                    <strong>Niveau:</strong> 
                                    <?php $__currentLoopData = $niveaux ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $niveau): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php if($niveau->id == $newInscription['niveau_id']): ?>
                                            <?php echo e($niveau->nom); ?>

                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </form>
        </div>
        
        <div class="modal-footer">
            <div class="d-flex justify-content-between w-100">
                <div>
                    <!-- Statut de sauvegarde -->
                    <?php if($isLoading ?? false): ?>
                    <span class="text-muted">
                        <i class="fa fa-spinner fa-spin me-1"></i>Ajout en cours...
                    </span>
                    <?php endif; ?>
                </div>
                <div>
                    <button type="button" class="btn btn-outline-secondary" wire:click="closeAddInscriptionModal">
                        <i class="fa fa-times me-1"></i>Annuler
                    </button>
                    <button type="submit" class="btn btn-success" form="addInscriptionForm" wire:loading.attr="disabled" wire:click="addInscription">
                        <span wire:loading wire:target="addInscription" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                        <i class="fa fa-plus me-1" wire:loading.remove wire:target="addInscription"></i>
                        Ajouter l'inscription
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<style>
/* Styles pour le modal d'ajout d'inscription */
.student-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.livewire-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1050;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    animation: fadeIn 0.3s ease-out;
}

.livewire-modal-content {
    background: white;
    border-radius: 1rem;
    border: none;
    box-shadow: 0 20px 60px rgba(0,0,0,0.2);
    max-height: 90vh;
    overflow-y: auto;
    width: 100%;
    max-width: 800px;
    animation: slideInUp 0.3s ease-out;
}

.livewire-modal-content.modal-lg {
    max-width: 800px;
}

.modal-header {
    border-radius: 1rem 1rem 0 0;
    border-bottom: none;
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 1rem 1rem;
    padding: 1.5rem;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from { 
        transform: translateY(50px);
        opacity: 0;
    }
    to { 
        transform: translateY(0);
        opacity: 1;
    }
}

.form-label {
    margin-bottom: 0.5rem;
    color: #495057;
}

.form-select, .form-control {
    border-radius: 0.5rem;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-select:focus, .form-control:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.alert {
    border-radius: 0.75rem;
    border: none;
}

.btn {
    border-radius: 0.5rem;
    padding: 0.5rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* Responsive */
@media (max-width: 768px) {
    .livewire-modal-content {
        margin: 0.5rem;
        max-width: calc(100% - 1rem);
    }
    
    .modal-body, .modal-footer {
        padding: 1rem;
    }
}
</style>

<script>
// Raccourcis clavier pour le modal d'ajout d'inscription
document.addEventListener('keydown', function(e) {
    const modal = document.querySelector('.livewire-modal-overlay');
    if (modal) {
        // Escape pour fermer
        if (e.key === 'Escape') {
            e.preventDefault();
            window.livewire.find('<?php echo e($_instance->id); ?>').call('closeAddInscriptionModal');
        }
        
        // Ctrl+S pour sauvegarder
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            window.livewire.find('<?php echo e($_instance->id); ?>').call('addInscription');
        }
    }
});

// Gestion du focus automatique
document.addEventListener('livewire:load', function () {
    Livewire.hook('message.processed', (message, component) => {
        // Focus sur le premier champ quand le modal s'ouvre
        setTimeout(() => {
            const modal = document.querySelector('.livewire-modal-overlay');
            if (modal) {
                const firstSelect = modal.querySelector('select');
                if (firstSelect) {
                    firstSelect.focus();
                }
            }
        }, 100);
    });
});
</script>
<?php /**PATH C:\xampp\htdocs\ImsaaProject\resources\views/components/add-inscription-modal.blade.php ENDPATH**/ ?>